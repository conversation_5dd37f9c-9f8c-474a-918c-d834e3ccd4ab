import { OrderStatusEnums } from '@/types/enums/orderStatus';

export type IOrserStatus =
  | 'Draft'
  | 'Submitted'
  | 'Pending'
  | 'Assigned'
  | 'In_Progress'
  | 'Completed'
  | 'Cancelled';
export interface CreateOrderItemDto {
   "id": "550e8400-e29b-41d4-a716-446655440000",
      "tenantId": "550e8400-e29b-41d4-a716-446655441111",
      "name": "Standard Box",
      "description": "Standard box for most shipments",
      "status": "Active",
      "capabilities": [
        "Box",
        "Envelope",
        "Pallet"
      ],
      "dimensionsRequired": true,
      "weightRequired": true,
      "maxWeight": 50.5,
      "maxVolume": 100.5,
      "priceCalculationRules": {
        "basePrice": 10,
        "weightFactor": 0.5,
        "distanceFactor": 0.1
      },
      "requiresSignature": false,
      "requiresInsurance": false,
      "specialHandlingInstructions": "Handle with care. Keep upright.",
      "vehicleTypeRestrictions": [
        "550e8400-e29b-41d4-a716-446655440000"
      ],
      "availableZones": [
        "550e8400-e29b-41d4-a716-446655440001"
      ],
      "metadata": {
        "customField1": "value1",
        "customField2": "value2"
      },
      "createdAt": "2023-01-15T08:30:00.000Z",
      "updatedAt": "2023-01-15T08:30:00.000Z",
      "createdBy": "550e8400-e29b-41d4-a716-446655442222",
      "updatedBy": "550e8400-e29b-41d4-a716-446655442222"
}

export interface ResponseOrderItemDto extends CreateOrderItemDto {
  id: string;
  orderId: string;
  createdAt: string;
  updatedAt: string;
  packageTemplateName?: string;
}

export interface IOrderItemPaginatedResponse {
  data: ResponseOrderItemDto[];
  total: number;
  skip: number;
  limit: number;
}

export interface CreateOrdersDto {
  referenceNumber?: string;
  trackingNumber: string;
  status?: string;
  customerId: string;
  requestedById: string;
  submittedById: string;
  packageTemplateId?: string;
  collectionAddressId: string;
  collectionContactName?: string;
  collectionInstructions?: string;
  collectionSignatureRequired?: boolean;
  assignedVehicleDescription: string;
  scheduledCollectionTime?: string;
  collectionZoneId?: string;
  deliveryAddressId: string;
  deliveryContactName?: string;
  deliveryInstructions?: string;
  deliverySignatureRequired?: boolean;
  scheduledDeliveryTime?: string;
  deliveryZoneId?: string;
  totalItems?: number;
  totalVolume?: number;
  isLocked: boolean;
  totalWeight?: number;
  declaredValue?: number;
  vehicleTypeId?: string;
  priceSet?: string;
  assignedDriverId?: string;
  assignedVehicleId?: string;
  codAmount?: number;
  priceSetId?: string;
  basePriceType?: string;
  distance?: number;
  distanceUnit?: string;
  basePrice: string;
  codCollected?: boolean;
  optionsPrice?: number;
  description?: string;
  totalPrice?: number;
  paymentStatus: string;
  billingStatus?: string;
  miscAdjustment?: number;
  comments?: string;
  internalNotes?: string;
  items?: CreateOrderItemDto[];
  serviceLevel?: string;
  customFields?: {
    [key: string]: any;
  };
  metadata?: {
    [key: string]: any;
  };
}

export interface IResponseOrderDto extends CreateOrdersDto {
  id: string;
  orderId: string;
  createdAt: string;
  updatedAt: string;
}

export interface IOrderPaginatedResponse {
  data: IResponseOrderDto[];
  total: number;
  skip: number;
  limit: number;
}

export interface IUpdateOrderStatusPayload {
  status: OrderStatusEnums;
  reason: string;
  comments: string;
}

export interface IOrderPackages {
  id: string;
  orderId: string;
  name: string;
  description: string;
  quantity: number;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  price: number;
}
